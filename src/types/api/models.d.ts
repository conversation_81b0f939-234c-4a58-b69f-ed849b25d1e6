declare namespace API {
  declare namespace Models {
    interface Model {
      id: number
      name: string
      source: 'huggingface' | 'ollama_library' | 'model_scope' | 'local_path'
      huggingface_repo_id?: string
      huggingface_filename?: string
      ollama_library_model_name?: string
      model_scope_model_id?: string
      model_scope_file_path?: string
      local_path?: string
      description?: string
      meta?: Record<string, unknown>
      replicas: number
      ready_replicas: number
      categories: string[]
      placement_strategy: 'spread' | 'binpack'
      cpu_offloading: boolean
      distributed_inference_across_workers: boolean
      worker_selector?: Record<string, string>
      gpu_selector?: {
        gpu_ids?: string[]
      }
      backend: 'vllm' | 'llama-box' | 'vox-box'
      backend_version?: string
      backend_parameters?: string[]
      created_at: string
      updated_at: string
    }

    type CreateModel = Omit<Model, 'id' | 'created_at' | 'updated_at' | 'ready_replicas'>
    type UpdateModel = CreateModel

    interface ModelInstance {
      id: number
      name: string
      source: 'huggingface' | 'ollama_library' | 'model_scope' | 'local_path'
      huggingface_repo_id?: string
      huggingface_filename?: string
      ollama_library_model_name?: string
      model_scope_model_id?: string
      model_scope_file_path?: string
      local_path?: string
      worker_id?: number
      worker_name?: string
      worker_ip?: string
      pid?: number
      port?: number
      download_progress?: number
      state:
        | 'initializing'
        | 'pending'
        | 'starting'
        | 'running'
        | 'scheduled'
        | 'error'
        | 'downloading'
        | 'analyzing'
        | 'unreachable'
      state_message?: string
      computed_resource_claim?: {
        is_unified_memory?: boolean
        offload_layers?: number
        total_layers?: number
        ram?: number
        vram?: Record<string, number>
        tensor_split?: number[]
      }
      gpu_indexes?: number[]
      model_id: number
      model_name: string
      distributed_servers?: {
        rpc_servers?: {
          pid?: number
          port?: number
          gpu_index?: number
          worker_id?: number
          computed_resource_claim?: {
            is_unified_memory?: boolean
            offload_layers?: number
            total_layers?: number
            ram?: number
            vram?: Record<string, number>
            tensor_split?: number[]
          }
        }[]
      }
      created_at: string
      updated_at: string
    }
  }

  type CreateModelInstance = {
    name: string
    source: 'huggingface' | 'ollama_library' | 'model_scope' | 'local_path'
    huggingface_repo_id?: string
    huggingface_filename?: string
    ollama_library_model_name?: string
    model_scope_model_id?: string
    model_scope_file_path?: string
    local_path?: string
    worker_id?: number
    worker_name?: string
    worker_ip?: string
    gpu_indexes?: number[]
    model_id: number
    model_name: string
  }
  type UpdateModelInstance = CreateModelInstance
}
