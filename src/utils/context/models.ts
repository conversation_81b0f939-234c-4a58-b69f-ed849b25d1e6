import type { ShallowRef } from 'vue'

import type { FormValues } from '@/components/models/ModelDeployForm.vue'

type ModelId = string

export const modelsContextInjectionKey = Symbol('models') as InjectionKey<{
  selectedModels: Readonly<ShallowRef<ModelId[]>>
  setSelectedModels(value: ModelId[]): void

  deployFormInitialValues: Readonly<ShallowRef<Partial<FormValues>>>
  setDeployFormInitialValues(value: Partial<FormValues>): void

  isDeployModalOpen: Readonly<Ref<boolean>>
  setIsDeployModalOpen(value: boolean): void

  formAction: Readonly<Ref<'create' | 'update'>>
  setFormAction(value: 'create' | 'update'): void

  rawModels: Readonly<ShallowRef<API.Models.Model[]>>
}>

export function injectModelsContext() {
  const context = inject(modelsContextInjectionKey)
  if (!context) {
    throw new Error('modelsContext is not provided')
  }
  return context
}
