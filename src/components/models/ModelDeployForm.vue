<script lang="ts">
  export interface FormValues {
    name: string
    source: string
    framework: string
    replicaNum: number
    modelType: string
    vcardNum?: number
    parameters?: string[]
  }
</script>

<script setup lang="ts">
  import type { FormProps } from 'ant-design-vue'

  import { injectModelsContext } from '@/utils/context/models'

  import IconHelp from '~icons/lucide/circle-help'
  import IconPlus from '~icons/lucide/plus'
  import IconMinus from '~icons/lucide/minus'

  const props = defineProps<FormProps>()

  const { deployFormInitialValues, formAction } = injectModelsContext()

  const formValues = ref<Partial<FormValues> & { parameters: string[] }>({
    parameters: [],
  })
  watchEffect(() => {
    formValues.value = {
      ...deployFormInitialValues.value,
      parameters: deployFormInitialValues.value.parameters ?? [],
    }
  })
</script>

<template>
  <AForm
    v-bind="props"
    :model="formValues"
    layout="vertical"
    class="space-y-4!"
  >
    <AFormItem
      label="名称"
      name="name"
      required
    >
      <AInput v-model:value="formValues.name" />
    </AFormItem>

    <AFormItem
      label="模型路径"
      name="source"
      required
    >
      <AInput v-model:value="formValues.source" />

      <template #tooltip>
        <ATooltip>
          <IconHelp class="ml-1 size-3.5 cursor-help" />
          <template #title>
            <ol className="m-0! list-decimal space-y-2 pl-5 text-xs">
              <li>
                GGUF 格式模型：
                <br />
                指定模型文件，例如: /usr/local/models/model.gguf
              </li>
              <li>
                分片 GGUF 格式模型：
                <br />
                指定模型的第一个分片文件，例如: /usr/local/models/model-00001-of-00004.gguf
              </li>
              <li>
                Safetensors 格式模型：
                <br />
                指定包含 .safetensors 和 config.json 文件的模型目录，例如: /usr/local/models/model/
              </li>
            </ol>
          </template>
        </ATooltip>
      </template>
    </AFormItem>

    <AFormItem
      label="加速框架"
      name="framework"
      required
    >
      <AInput
        v-model:value="formValues.framework"
        disabled
      />
    </AFormItem>

    <AFormItem
      label="副本数"
      name="replicaNum"
      required
    >
      <AInputNumber
        v-model:value="formValues.replicaNum"
        :min="0"
        :max="100"
        :disabled="formAction === 'update'"
        class="w-full!"
      />
    </AFormItem>

    <AFormItem
      label="模型类别"
      name="modelType"
      required
    >
      <ASelect
        default-value="llm"
        v-model:value="formValues.modelType"
      >
        <ASelectOption value="llm">LLM</ASelectOption>
        <ASelectOption value="embedding">Embedding</ASelectOption>
      </ASelect>
    </AFormItem>

    <AFormItem
      label="显卡张数"
      name="vcardNum"
    >
      <AInputNumber
        v-model:value="formValues.vcardNum"
        class="w-full!"
        :min="0"
        :max="100"
      />
    </AFormItem>

    <div>
      <div class="flex items-center pb-2">
        <div>后端参数</div>
        <ATooltip>
          <IconHelp class="ml-1 size-3.5 cursor-help" />
          <template #title>
            <span class="text-xs">
              vLLM 参数请参考
              <a
                href="https://docs.vllm.ai/en/stable/serving/openai_compatible_server.html#cli-reference"
                target="_blank"
                rel="noreferrer"
              >
                文档
              </a>
            </span>
          </template>
        </ATooltip>
      </div>

      <div class="space-y-4 rounded-md border p-5">
        <template v-if="formValues.parameters.length > 0">
          <div
            class="flex items-center gap-2.5"
            v-for="(parameter, index) in formValues.parameters"
            :key="index + parameter"
          >
            <AFormItem
              class="m-0! w-full!"
              :name="['parameter', index]"
              :rules="[{ required: true, message: '请输入参数' }]"
            >
              <AInput
                autofocus
                v-model:value="formValues.parameters[index]"
              />
            </AFormItem>

            <AButton
              shape="circle"
              size="small"
              class="flex! items-center justify-center p-0!"
              @click="formValues.parameters.splice(index, 1)"
            >
              <IconMinus />
            </AButton>
          </div>
        </template>

        <div class="flex items-center gap-2.5">
          <button
            class="flex w-full cursor-pointer items-center justify-center gap-2 rounded-md bg-[#f0f0f0] p-2 transition-colors hover:bg-[#e0e0e0]"
            @click.prevent="formValues.parameters.push('')"
          >
            <IconPlus />
            添加参数
          </button>

          <div
            class="w-6"
            v-if="formValues.parameters.length > 0"
          />
        </div>
      </div>
    </div>
  </AForm>
</template>
