<script lang="ts">
  import type { ColumnType } from 'ant-design-vue/es/table'

  export interface TableItem {
    id: string
    name: string
    status: API.Models.ModelInstance['state']
    createTime: Date
  }

  const columns = [
    { width: 80, key: 'expansion-placeholder' },
    {
      title: '实例名称',
      dataIndex: 'name',
      key: 'name',
      width: 560,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 160,
    },
    {
      title: '创建时间',
      key: 'createTime',
      customRender: ({ record }) => record.createTime.toLocaleString('zh-CN'),
      width: 200,
    },
    { key: 'action-placeholder', width: 160 },
  ] as const satisfies ColumnType<TableItem>[]
</script>

<script setup lang="ts">
  const props = defineProps<{
    modelId: string
  }>()

  const { data: instancesFetched, status } = useQuery({
    queryKey: modelsQueryKey.modelInstanceList(props.modelId),
    async queryFn(): Promise<TableItem[]> {
      const { items } = await modelsFetcher<{ items: API.Models.ModelInstance[] }>(
        `/models/${props.modelId}/instances/`,
      )
      return items.map((item) => ({
        id: String(item.id),
        name: item.name,
        createTime: new Date(item.created_at),
        status: item.state,
      }))
    },
    refetchInterval: 3000,
  })
</script>

<template>
  <ATable
    :columns="columns"
    :data-source="instancesFetched"
    class="[&_.ant-table]:-mx-4! **:[.ant-table-cell]:first:border-none! **:[th]:border-t-0!"
    :pagination="false"
    :scroll="{ x: 1200 }"
    :loading="status === 'pending'"
  >
    <template
      #bodyCell="// @ts-expect-error antdv poor typing
      { column, record }: { record: TableItem; column: (typeof columns)[number] }"
    >
      <template v-if="column.key === 'status'">
        <ModelInstanceStatusTag :status="record.status" />
      </template>
    </template>
  </ATable>
</template>
